/**
 * 视频相关API
 */
import request from '../utils/request';

/**
 * 获取视频列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码，从1开始
 * @param {number} params.pageSize - 每页数量
 * @param {string} [params.videoType] - 视频类型
 * @param {string} [params.categotyId] - 视频类型ID
 * @param {string} [params.videoTag] - 视频标签
 * @param {boolean} [params.isRecommended] - 是否推荐
 * @param {string} [params.sorting] - 排序方式
 * @returns {Promise<Object>} 视频列表和总数
 */
export function getVideoList(params) {
  logger.log('调用视频列表API:', params);

  const requestParams = {
    maxResultCount: params.pageSize || 20,
    skipCount: ((params.page || 1) - 1) * (params.pageSize || 20),
    sorting: params.sorting || 'VideoAddTime desc',
    filterText: params.filterText || null,
    isRecommended: params.isRecommended || null
  };

  // 优先使用categotyId，如果没有则使用videoType
  if (params.categotyId) {
    requestParams.categotyId = params.categotyId;
  } else if (params.videoType) {
    requestParams.videoType = params.videoType;
  }

  if (params.videoTag) {
    requestParams.videoTag = params.videoTag;
  }

  logger.log('实际发送的请求参数:', requestParams);
  return request.post('/api/v1/videos', requestParams);
}

/**
 * 获取视频详情
 * @param {string} id - 视频ID
 * @returns {Promise<Object>} 视频详情
 */
export function getVideoDetail(id) {
  logger.log('调用视频详情API:', id);
  return request.post(`/api/v1/videos/${id}`);
}

/**
 * 获取随机视频列表
 * @param {Object} params - 查询参数
 * @param {string} [params.videoType] - 视频类型
 * @param {number} [params.count=12] - 返回数量
 * @returns {Promise<Array>} 视频列表
 */
export function getRandomVideos(params) {
  logger.log('调用随机视频API:', params);

  // 创建请求参数对象
  const requestParams = {
    maxResultCount: params.maxResultCount || params.count || 12,
    skipCount: params.skipCount || 0,
    sorting: params.sorting || 'Random' // 如果没有指定排序方式，使用 Random
  };

  // 只在参数存在时才添加到请求中，避免发送null值
  if (params.videoType) {
    requestParams.videoType = params.videoType;
  }

  if (params.videoTag) {
    requestParams.videoTag = params.videoTag;
  }

  if (params.isRecommended !== undefined) {
    requestParams.isRecommended = params.isRecommended;
  }

  // 处理extraParams参数
  if (params.extraParams !== undefined) {
    requestParams.extraParams = params.extraParams;
  }

  logger.log('实际发送的请求参数:', requestParams);
  return request.post('/api/v1/videos/random', requestParams);
}

/**
 * 搜索视频
 * @param {Object} params - 搜索参数
 * @param {string} params.keyword - 搜索关键词
 * @param {number} params.page - 页码，从1开始
 * @param {number} params.pageSize - 每页数量
 * @param {string} [params.videoType] - 视频类型
 * @param {string} [params.videoTag] - 视频标签
 * @returns {Promise<Object>} 搜索结果和总数
 */
export function searchVideos(params) {
  logger.log('调用视频搜索API:', params);
  return request.post('/api/v1/videos/search', {
    filterText: params.keyword,
    maxResultCount: params.pageSize || 20,
    skipCount: ((params.page || 1) - 1) * (params.pageSize || 20),
    sorting: params.sorting || 'VideoScore desc',
    videoType: params.videoType || null,
    videoTag: params.videoTag || null
  });
}

// 导入数据解析工具
import { parseVideoData } from '@/utils/dataParser';
import logger from '@/utils/logger';

// 导出数据解析函数
export { parseVideoData };

/**
 * 从视频数据中提取年份
 * @param {Object} video - 视频数据
 * @returns {string} 年份
 */
function getVideoYear(video) {
  // 如果有明确的年份字段，直接使用
  if (video.videoYear) return video.videoYear;

  // 尝试从发布时间提取年份
  if (video.videoAddTime) {
    const match = video.videoAddTime.match(/(\d{4})/);
    if (match) return match[1];
  }

  // 默认返回空字符串
  return '';
}



