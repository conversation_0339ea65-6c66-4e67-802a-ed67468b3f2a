<template>
  <view class="container">
    <view class="content-area" @scroll="handleScroll">
      <!-- 顶部跟随栏，添加动态样式和点击事件 -->
      <view class="topbar" :style="{ opacity: topbarOpacity }" @click="scrollToTop">
        <text>{{ topbarTitle }}</text>
        <i class="icon xiaji<PERSON>ou"></i>
      </view>

      <!-- 加载中提示 -->
      <view class="loading-container" v-if="loading && !videos.length">
        <view class="loading-spinner"></view>
        <text class="loading-text">正在加载数据...</text>
      </view>

      <!-- 错误提示 -->
      <view class="error-container" v-else-if="error && !videos.length">
        <text class="error-text">{{ error }}</text>
        <view class="error-retry" @click="loadVideos">点击重试</view>
      </view>

      <!-- 过滤条件区域 -->
      <view class="filter-container" v-else>
        <!-- 排序方式 -->
        <view class="filter-row">
          <view class="filter-scroll">
            <ul class="filter-list">
              <li
                v-for="(sort, index) in sortOptions"
                :key="index"
                :class="{ active: currentSort === sort.value }"
                @click="changeSort(sort.value)"
              >
                {{ sort.label }}
              </li>
            </ul>
          </view>
        </view>

        <!-- 类型筛选（只在没有视频类型时显示） -->
        <view class="filter-row" v-if="categoryOptions.length > 0 && !videoType">
          <view class="filter-scroll">
            <ul class="filter-list">
              <li
                :class="{ active: currentCategory === '' }"
                @click="changeCategory('')"
              >
                全部类型
              </li>
              <li
                v-for="(category, index) in categoryOptions"
                :key="index"
                :class="{ active: currentCategory === category.value }"
                @click="changeCategory(category.value)"
              >
                {{ category.label }}
              </li>
            </ul>
          </view>
        </view>

        <!-- 喜好筛选（始终显示，确保所有栏目都有“全部喜好”选项） -->
        <view class="filter-row" v-if="videoType || videoTypeId">
          <view class="filter-scroll">
            <ul class="filter-list">
              <li
                :class="{ active: currentTag === '' }"
                @click="changeTag('')"
              >
                全部喜好
              </li>
              <li
                v-for="(tag, index) in tagOptions"
                :key="index"
                :class="{ active: currentTag === tag.value }"
                @click="changeTag(tag.value)"
              >
                {{ tag.label }}
              </li>
            </ul>
          </view>
        </view>

        <!-- 推荐筛选 -->
        <view class="filter-row">
          <view class="filter-scroll">
            <ul class="filter-list">
              <li
                :class="{ active: isRecommended === null }"
                @click="changeRecommended(null)"
              >
                全部推荐
              </li>
              <li
                :class="{ active: isRecommended === true }"
                @click="changeRecommended(true)"
              >
                是
              </li>
              <li
                :class="{ active: isRecommended === false }"
                @click="changeRecommended(false)"
              >
                否
              </li>
            </ul>
          </view>
        </view>

        <!-- 地区、语言和年份筛选已移除，因为我们没有从 API 获取这些数据 -->
        <!-- 如果将来有相应的 API，可以添加代码来显示这些筛选项 -->
      </view>

      <!-- 视频列表 -->
      <view v-if="!loading && !error">
        <!-- 竖屏视频列表 -->
        <VerticalVideoList v-if="!isHorizontalLayout" :videoList="videos" />

        <!-- 横屏视频列表 -->
        <HorizontalVideoList v-else :videoList="videos" />

        <!-- 无数据提示 -->
        <view class="no-data" v-if="videos.length === 0">
          <text>暂无数据</text>
        </view>

        <!-- 加载状态提示 -->
        <view class="load-more" v-if="videos.length > 0">
          <view v-if="loadingMore" class="loading">加载中...</view>
          <view v-else-if="!hasMore" class="no-more">没有更多数据了</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { getVideoList } from '@/api/video';
import { getVideoTagList, TagDisplayType } from '@/api/videoTag';
import { getVideoTypeList, TypeDisplayType } from '@/api/videoType';
import { getCachedNavigationList } from '@/api/navigationCache'; // 使用缓存服务获取导航数据
import VerticalVideoList from '@/components/common/VerticalVideoList.vue';
import HorizontalVideoList from '@/components/common/HorizontalVideoList.vue';
import logger from '@/utils/logger';

export default {
  components: {
    VerticalVideoList,
    HorizontalVideoList
  },
  data() {
    return {
      // 基础数据
      topbarOpacity: 0, // 初始透明度为0
      scrollTop: 0, // 滚动距离
      topbarTitle: '全部', // 顶部标题

      // 视频数据
      videos: [],
      loading: true,
      error: null,
      currentPage: 1, // 当前页码
      hasMore: true, // 是否还有更多数据
      loadingMore: false, // 是否正在加载更多
      pageSize: 12, // 每页数量

      // 过滤条件
      videoType: '', // 视频类型，如电影、电视剧等
      videoTypeId: '', // 视频类型ID，用于加载标签数据
      currentSort: 'VideoAddTime desc', // 当前排序方式
      currentCategory: '', // 当前类型
      currentTag: '', // 当前标签
      isRecommended: null, // 是否推荐，null表示全部
      // 地区、语言和年份属性已移除，因为我们没有从 API 获取这些数据

      // 筛选选项
      sortOptions: [
        { label: '最新上传', value: 'VideoAddTime desc' },
        { label: '人气最高', value: 'VideoHits desc' },
        { label: '评分最高', value: 'VideoScore desc' },
        { label: '最近更新', value: 'VideoUpdateTime desc' }
      ],
      categoryOptions: [], // 类型选项，从服务器加载
      tagOptions: [], // 标签选项，从服务器加载
      areaOptions: [], // 地区选项，从服务器加载
      langOptions: [], // 语言选项，从服务器加载
      yearOptions: [],

      // 横屏竖屏相关
      navigations: [], // 导航数据
      isHorizontalLayout: false // 是否为横屏布局
    };
  },
  methods: {
    // 回到顶部
    scrollToTop() {
      // 使用uni-app的API滚动到页面顶部
      uni.pageScrollTo({
        scrollTop: 0,
        duration: 300 // 平滑滚动效果，300毫秒
      });

      // 重置滚动位置和透明度
      this.scrollTop = 0;
      this.topbarOpacity = 0;
    },

    // 处理滚动事件
    handleScroll(e) {
      // 获取滚动距离
      this.scrollTop = e.detail.scrollTop;

      // 设置初始隐藏阈值和完全显示阈值
      const hideThreshold = 100; // 低于此值完全隐藏
      const showThreshold = 200; // 高于此值完全显示

      // 计算透明度：从0到1的渐变
      if (this.scrollTop <= hideThreshold) {
        // 滚动距离小于初始阈值时完全隐藏
        this.topbarOpacity = 0;
      } else if (this.scrollTop >= showThreshold) {
        // 滚动超过显示阈值时完全显示
        this.topbarOpacity = 1;
      } else {
        // 在两个阈值之间，透明度线性增加
        // 将100-300之间的值映射到0-1之间
        this.topbarOpacity = (this.scrollTop - hideThreshold) / (showThreshold - hideThreshold);
      }
    },

    /**
     * 加载筛选条件数据
     */
    async loadTags() {
      try {
        // 如果当前有视频类型（如电影、动漫等），则不加载导航数据（类型选项）
        // 因为当前已经在特定的栏目中，不需要再切换到其他栏目
        if (!this.videoType) {
          try {
            // 使用缓存服务获取导航列表
            const navigationList = await getCachedNavigationList();
            if (navigationList && navigationList.length > 0) {
              // 将导航数据转换为类型选项
              this.categoryOptions = navigationList.map(nav => ({
                label: nav.categotyName,
                value: nav.categotyId
              }));
            } else {
              this.categoryOptions = [];
            }
          } catch (error) {
            this.categoryOptions = [];
          }
        } else {
          // 如果已经在特定栏目中，不显示类型选项
          this.categoryOptions = [];
        }

        // 加载标签数据（喜好选项）
        try {
          // 优先使用从 URL 参数中获取的 videoTypeId
          let tagSourceId = this.videoTypeId || '';

          // 如果没有 videoTypeId，但有当前类型，使用当前类型的ID
          if (!tagSourceId && this.currentCategory) {
            tagSourceId = this.currentCategory;
          }

          // 如果还是没有ID，但有栏目名称，使用默认ID
          if (!tagSourceId && this.videoType) {
            // 使用默认的电影ID，确保所有栏目都能加载标签数据
            // 这里使用一个默认ID，直到loadCategoryId方法从接口获取到正确的ID
            tagSourceId = '3a196546-b89a-f449-aaa0-275a25abe91f';
          }


          // 始终尝试加载标签数据，即使没有tagSourceId
          // 这样可以确保所有栏目都显示“全部喜好”选项
          // 构造请求参数
          const payload = {
            videoTypeId: tagSourceId || '3a196546-b89a-f449-aaa0-275a25abe91f', // 确保始终有一个ID
            displayTypes: ['1']
          };


          // 直接使用正确的参数格式调用API
          // 这里我们需要确保使用正确的API调用方式
          // 如果 getVideoTagList 支持直接传入对象，则使用以下方式：
          // const tags = await getVideoTagList(payload);
          // 否则，使用分开的参数：
          const tags = await getVideoTagList(payload.displayTypes, payload.videoTypeId);

          if (tags && tags.length > 0) {
            // 将标签数据转换为选项格式
            this.tagOptions = tags.map(tag => ({
              label: tag.tagName,
              value: tag.tagName
            }));
          } else {
            this.tagOptions = [];
          }
        } catch (error) {
          this.tagOptions = [];
        }

        // 如果有videoTypeId，尝试从 API 获取地区、语言和年份数据
        if (this.videoTypeId || this.videoType) {
          try {
            // 清空现有的选项
            this.areaOptions = [];
            this.langOptions = [];
            this.yearOptions = [];

            // 这里可以添加从 API 获取地区、语言和年份数据的代码
            // 如果有相应的 API，可以调用它们来获取数据

            // 暂时不显示这些筛选项，因为我们没有从 API 获取它们
            // 如果将来有相应的 API，可以添加代码来获取这些数据
          } catch (error) {
            this.areaOptions = [];
            this.langOptions = [];
            this.yearOptions = [];
          }
        }



      } catch (error) {
        // 出错时清空所有选项
        this.categoryOptions = [];
        this.tagOptions = [];
        this.areaOptions = [];
        this.langOptions = [];
        this.yearOptions = [];
      }
    },

    /**
     * 加载视频数据
     * @param {boolean} isLoadMore - 是否是加载更多
     */
    async loadVideos(isLoadMore = false) {
      // 如果正在加载或者没有更多数据，则返回
      if ((isLoadMore && this.loadingMore) || (isLoadMore && !this.hasMore)) {
        return;
      }

      try {
        // 设置加载状态
        if (isLoadMore) {
          this.loadingMore = true;
        } else {
          this.loading = true;
          this.currentPage = 1; // 重置页码
          this.hasMore = true; // 重置是否有更多数据
        }

        this.error = null;

        // 设置请求参数
        const params = {
          page: isLoadMore ? this.currentPage + 1 : 1,
          pageSize: this.pageSize,
          sorting: this.currentSort
        };

        // 添加筛选条件
        if (this.videoType) {
          params.videoType = this.videoType;
        }

        // 如果选中了类型，需要根据类型ID查找对应的类型名称
        if (this.currentCategory) {
          // 查找对应的类型名称
          const selectedCategory = this.categoryOptions.find(c => c.value === this.currentCategory);
          if (selectedCategory) {
            params.videoType = selectedCategory.label; // 使用类型名称而不是ID
          }
        }

        if (this.currentTag) {
          params.videoTag = this.currentTag;
        }

        if (this.isRecommended !== null) {
          // 使用布尔值而不是字符串
          params.isRecommended = this.isRecommended;
        }

        // 地区、语言和年份参数已移除，因为我们没有从 API 获取这些数据

        // 调用API获取视频列表
        const response = await getVideoList(params);

        // 检查 response 是否为数组或包含 items 属性
        if ((Array.isArray(response) && response.length > 0) || (response && response.items)) {
          // 处理视频数据
          const items = Array.isArray(response) ? response : response.items;
          const newVideos = items.map(item => ({
            // 原始字段（为了兼容组件）
            id: item.id,
            videoId: item.videoId,
            videoName: item.videoName,
            videoPic: item.videoPic || 'https://placehold.co/300x400',
            videoScore: item.videoScore,
            videoHits: item.videoHits,
            videoTag: item.videoTag,
            videoYear: item.videoYear,
            videoActor: item.videoActor,
            videoArea: item.videoArea,
            videoLang: item.videoLang,
            videoDirector: item.videoDirector,
            videoBlurb: item.videoBlurb,
            videoRemarks: item.videoRemarks,
            videoDuration: item.videoDuration,

            // 兼容字段（为了兼容原有代码）
            title: item.videoName,
            year: item.videoYear || (item.videoAddTime ? new Date(item.videoAddTime).getFullYear() + '年' : ''),
            tags: item.videoTag ? item.videoTag.split(',') : [],
            poster: item.videoPic || 'https://placehold.co/300x400',
            score: item.videoScore,
            hits: item.videoHits,
            actor: item.videoActor,
            area: item.videoArea,
            lang: item.videoLang,
            director: item.videoDirector,
            blurb: item.videoBlurb,
            remarks: item.videoRemarks
          }));

          // 判断是否还有更多数据
          this.hasMore = newVideos.length === this.pageSize;

          // 更新页码
          if (isLoadMore && newVideos.length > 0) {
            this.currentPage++;
            // 合并数据
            this.videos = [...this.videos, ...newVideos];
          } else {
            // 首次加载或切换分类时替换数据
            this.videos = newVideos;
          }


        } else {
          if (!isLoadMore) {
            this.videos = [];
          }
          this.hasMore = false;
        }
      } catch (error) {
        this.error = '加载视频失败，请重试';
      } finally {
        if (isLoadMore) {
          this.loadingMore = false;
        } else {
          this.loading = false;
        }
      }
    },

    /**
     * 加载更多数据
     */
    loadMore() {
      if (this.hasMore && !this.loadingMore) {
        this.loadVideos(true);
      }
    },

    /**
     * 处理页面触底事件
     */
    handleReachBottom() {
      this.loadMore();
    },

    /**
     * 更新顶部标题
     */
    updateTopbarTitle() {
      // 如果是从标签点击进入，保留原标题
      // 如果当前标签不为空，则说明是从标签进入
      if (this.currentTag && this.topbarTitle && this.topbarTitle !== '全部') {
        return; // 保留标签标题
      }

      // 只显示排序方式（最新上传、人气最高、评分最高、最近更新）
      const sortTitle = this.sortOptions.find(sort => sort.value === this.currentSort)?.label || '最新上传';

      // 直接使用排序方式作为顶部标题，不显示其他筛选条件
      this.topbarTitle = sortTitle;
    },

    /**
     * 切换排序方式
     * @param {string} sort - 排序方式
     */
    changeSort(sort) {
      if (this.currentSort === sort) return; // 避免重复加载

      this.currentSort = sort;
      this.currentPage = 1; // 重置页码
      this.hasMore = true; // 重置是否有更多数据

      // 更新顶部标题，显示当前排序方式
      this.updateTopbarTitle();

      this.loadVideos(); // 重新加载视频数据
    },

    /**
     * 切换类型
     * @param {string} category - 类型 ID
     */
    changeCategory(category) {
      if (this.currentCategory === category) return; // 避免重复加载

      this.currentCategory = category;
      this.currentPage = 1; // 重置页码
      this.hasMore = true; // 重置是否有更多数据

      // 切换类型后需要重新加载标签
      this.currentTag = ''; // 重置标签
      this.isRecommended = null; // 重置推荐状态
      // 地区、语言和年份重置已移除，因为我们没有从 API 获取这些数据

      // 如果选中了类型，则更新页面标题
      if (category) {
        const selectedCategory = this.categoryOptions.find(c => c.value === category);
        if (selectedCategory) {
          this.setPageTitle(selectedCategory.label);
          // 不设置顶部标题，因为顶部标题应该显示排序方式
        }
      }

      this.loadTags(); // 重新加载标签数据
      this.loadVideos(); // 重新加载视频数据
    },

    /**
     * 切换标签
     * @param {string} tag - 标签
     */
    changeTag(tag) {
      if (this.currentTag === tag) return; // 避免重复加载

      this.currentTag = tag;
      this.currentPage = 1; // 重置页码
      this.hasMore = true; // 重置是否有更多数据
      this.loadVideos(); // 重新加载视频数据
    },

    /**
     * 切换推荐状态
     * @param {boolean|null} recommended - 推荐状态，null表示全部
     */
    changeRecommended(recommended) {
      if (this.isRecommended === recommended) return; // 避免重复加载

      this.isRecommended = recommended;
      this.currentPage = 1; // 重置页码
      this.hasMore = true; // 重置是否有更多数据
      this.loadVideos(); // 重新加载视频数据
    },

    // 地区、语言和年份筛选方法已移除，因为我们没有从 API 获取这些数据
    // 如果将来有相应的 API，可以添加代码来实现这些方法

    // 不再需要自定义getNavigationList方法，直接使用缓存服务

    /**
     * 从接口加载栏目ID
     * @param {string} categoryName - 栏目名称
     */
    async loadCategoryId(categoryName) {
      try {
        // 使用缓存服务获取导航列表
        const navigationList = await getCachedNavigationList();

        if (navigationList && navigationList.length > 0) {
          // 查找匹配的栏目
          const navigation = navigationList.find(nav =>
            nav.categotyName === categoryName ||
            nav.title === categoryName ||
            nav.name === categoryName
          );

          if (navigation && navigation.categotyId) {
            // 更新videoTypeId
            this.videoTypeId = navigation.categotyId;

            // 重新加载标签数据
            this.loadTags();
          }
        }
      } catch (error) {
        // 出错时使用默认ID
        // 已经在onLoad中设置了默认ID，这里不需要再设置
      }
    },

    /**
     * 设置页面标题
     * @param {string} title - 页面标题
     */
    setPageTitle(title) {
      // 设置页面标题
      uni.setNavigationBarTitle({
        title: title
      });
    },

    /**
     * 加载导航数据
     */
    async loadNavigations() {
      try {
        const response = await getCachedNavigationList();
        if (response && Array.isArray(response)) {
          this.navigations = response;
          logger.info('导航数据加载成功:', response.length);
        } else {
          logger.warn('导航数据为空或格式错误');
          this.navigations = [];
        }
      } catch (error) {
        logger.error('加载导航数据失败:', error);
        this.navigations = [];
      }
    },

    /**
     * 判断当前视频类型是否为横屏布局
     */
    checkIfHorizontalLayout() {
      logger.info('开始判断视频布局类型...');
      logger.info('当前视频类型:', this.videoType);
      logger.info('导航数据数量:', this.navigations.length);

      // 如果没有视频类型或导航数据，默认为竖屏
      if (!this.videoType || !this.navigations.length) {
        logger.warn('数据不完整，默认为竖屏布局');
        this.isHorizontalLayout = false;
        return;
      }

      // 将视频类型按逗号分割，以匹配导航数据
      const videoTypes = this.videoType.split(',').map(type => type.trim());
      logger.info('分割后的视频类型:', videoTypes);

      // 查找匹配的导航项
      const currentNavItem = this.navigations.find(nav => {
        return videoTypes.some(type =>
          nav.categotyName === type ||
          nav.name === type
        );
      });

      logger.info('匹配的导航项:', currentNavItem);

      if (currentNavItem && currentNavItem.extraParams) {
        try {
          logger.info('原始extraParams:', currentNavItem.extraParams);

          // 先尝试解析为JSON
          if (typeof currentNavItem.extraParams === 'string') {
            const extraParamsObj = JSON.parse(currentNavItem.extraParams);
            logger.info('解析后的extraParams:', extraParamsObj);
            this.isHorizontalLayout = extraParamsObj && extraParamsObj.isHorizontalLayout === true;
          } else {
            // 如果不是字符串，直接使用
            this.isHorizontalLayout = currentNavItem.extraParams && currentNavItem.extraParams.isHorizontalLayout === true;
          }

          logger.info('最终判断结果 - 当前视频类型:', this.videoType, '是否横屏:', this.isHorizontalLayout);
        } catch (error) {
          logger.error('解析extraParams失败:', error, currentNavItem.extraParams);
          // 兼容旧版本：检查是否为 '0'
          this.isHorizontalLayout = currentNavItem.extraParams === '0';
          logger.info('兼容性判断结果:', this.isHorizontalLayout);
        }
      } else {
        logger.warn('没有找到匹配的导航项');
        logger.info('尝试匹配的视频类型:', videoTypes);
        logger.info('可用的导航项categotyName:', this.navigations.map(nav => nav.categotyName));

        // 特殊情况：如果视频类型包含“短剧”，直接判断为横屏
        if (videoTypes.some(type => type.includes('短剧'))) {
          this.isHorizontalLayout = true;
          logger.info('基于视频类型包含“短剧”判断为横屏');
        } else {
          logger.info('没有找到对应的导航项或extraParams，默认为竖屏');
          this.isHorizontalLayout = false;
        }
      }
    }
  },
  // 页面生命周期函数
  onLoad(options) {

    // 获取URL参数
    if (options) {
      // 设置视频类型
      if (options.videoType) {
        this.videoType = options.videoType;
      }

      // 设置视频类型ID
      if (options.videoTypeId) {
        this.videoTypeId = options.videoTypeId;
      }

      // 设置排序方式
      if (options.sorting) {
        this.currentSort = options.sorting;
      }

      // 设置标签
      if (options.videoTag) {
        this.currentTag = decodeURIComponent(options.videoTag);
      }

      // 设置分类
      if (options.category) {
        // 如果有分类参数，更新顶部标题
        const categoryMap = {
          'all': '全部',
          'latest': '最新上传',
          'popular': '人气高',
          'rating': '评分高',
          'updated': '最近更新'
        };

        if (categoryMap[options.category]) {
          this.topbarTitle = categoryMap[options.category];
        }
      }

      // 设置页面标题
      if (options.pageTitle) {
        // 如果有传入页面标题（如电影、动漫、连续剧等），设置页面标题
        const pageTitle = decodeURIComponent(options.pageTitle);
        this.setPageTitle(pageTitle);

        // 记录当前栏目类型，用于筛选条件的显示隐藏
        this.videoType = pageTitle;

        // 如果没有videoTypeId，但有栏目名称，则从接口获取栏目ID
        if (!this.videoTypeId && pageTitle) {
          // 先设置一个默认ID，以防接口调用失败
          const defaultTypeId = '3a196546-b89a-f449-aaa0-275a25abe91f';
          this.videoTypeId = defaultTypeId;

          // 异步加载栏目ID
          this.loadCategoryId(pageTitle);
        }
      } else if (options.title) {
        // 如果有传入标题，使用标题作为顶部标题和页面标题
        const title = decodeURIComponent(options.title);
        this.topbarTitle = title;
        this.setPageTitle(title);
      } else if (this.videoType) {
        // 如果有视频类型，使用视频类型作为页面标题
        this.setPageTitle(this.videoType);
        this.topbarTitle = this.videoType;
      }
    }

    // 加载导航数据并判断横屏竖屏
    this.loadNavigations().then(() => {
      // 导航数据加载完成后，判断布局类型
      this.checkIfHorizontalLayout();
    });

    // 加载标签数据
    this.loadTags();

    // 加载视频数据
    this.loadVideos();

    // 更新顶部标题，显示排序方式
    this.updateTopbarTitle();

    // 监听页面滚动事件
    uni.$on('page-reach-bottom', this.handleReachBottom);
  },

  onUnload() {
    // 移除监听
    uni.$off('page-reach-bottom', this.handleReachBottom);
  },

  // 页面触底事件
  onReachBottom() {
    this.loadMore();
  },

  // 添加页面滚动监听
  onPageScroll(e) {
    // 获取滚动距离
    this.scrollTop = e.scrollTop;

    // 设置初始隐藏阈值和完全显示阈值
    const hideThreshold = 100; // 低于此值完全隐藏
    const showThreshold = 200; // 高于此值完全显示

    // 计算透明度：从0到1的渐变
    if (this.scrollTop <= hideThreshold) {
      // 滚动距离小于初始阈值时完全隐藏
      this.topbarOpacity = 0;
    } else if (this.scrollTop >= showThreshold) {
      // 滚动超过显示阈值时完全显示
      this.topbarOpacity = 1;
    } else {
      // 在两个阈值之间，透明度线性增加
      // 将100-300之间的值映射到0-1之间
      this.topbarOpacity = (this.scrollTop - hideThreshold) / (showThreshold - hideThreshold);
    }
  }
}
</script>

<style scoped>
/* 过滤条件区域 */
.filter-container {
  display: flex;
  gap: 10rpx;
  flex-direction: column;
  margin-bottom: 30rpx;
  padding-top: 20rpx;
}

.filter-row {
  width: 100%;
  height: 56rpx;
  color: #c8c8cc;
}

.filter-scroll {
  width: 100%;
  height: 100%;
  white-space: nowrap;
  overflow-x: scroll;
}

.filter-scroll::-webkit-scrollbar {
  display: none;
}

.filter-list {
  display: flex;
  height: 100%;
  flex-wrap: nowrap;
  gap: 20rpx;
}

.filter-list li {
  font-size: 28rpx;
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 0 20rpx;
}

.filter-list li.active {
  color: #fe748e;
  background: rgba(254, 116, 142, .1);
  border-radius: 28rpx;
}


/* 原有的电影列表样式已移除，现在使用组件来处理布局 */

.topbar {
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed; /* 改为fixed固定在顶部 */
  top: 44px;
  left: 0;
  width: 100%;
  background: #101012;
  height: 64rpx;
  color: #fe748e;
  font-weight: 600;
  z-index: 100; /* 确保在最上层 */
  transition: opacity 0.2s ease; /* 添加过渡效果 */
}


.video-info {
  position: absolute;
  width: calc(100% - 20rpx);
  padding: 0 10rpx;
  bottom: 0;
  height: 48rpx;
  font-size: 20rpx;
  line-height: 48rpx;
  background: linear-gradient(0deg, #101012, rgba(16, 16, 18, 0));
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.renqitime {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.renqitime span:first-child {
  display: flex;
  align-items: center;
}

.renqitime span:first-child:before {
  content: "";
  background: url("../../static/renqi.png") left center no-repeat;
  width: 24rpx;
  height: 24rpx;
  background-size: 100%;
  display: block;
  margin-right: 6rpx;
}

.pingfen {
  color: #ff743d;
  font-size: 30rpx;
  font-weight: 600;
  line-height: 30rpx;
}

/* 加载中和错误提示 */
.loading-container, .error-container, .no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200rpx;
  width: 100%;
  flex-direction: column;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #fe748e;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text, .error-text {
  font-size: 28rpx;
  color: #999;
}

.error-retry {
  margin-top: 20rpx;
  padding: 10rpx 30rpx;
  background-color: #fe748e;
  color: #fff;
  border-radius: 5rpx;
  font-size: 24rpx;
}

.no-data {
  color: #999;
  font-size: 28rpx;
}

/* 加载状态提示相关样式 */
.load-more {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80rpx;
  margin: 10rpx 0;
}

.loading, .no-more {
  color: #999;
  font-size: 28rpx;
}
</style>
